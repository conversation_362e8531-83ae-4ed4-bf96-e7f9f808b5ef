package com.sky.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MinIO配置属性类
 */
@Component
@ConfigurationProperties(prefix = "sky.minio")
@Data
public class MinioProperties {

    /**
     * MinIO服务端点
     */
    private String endpoint;

    /**
     * 访问密钥ID
     */
    private String accessKey;

    /**
     * 访问密钥Secret
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

}
