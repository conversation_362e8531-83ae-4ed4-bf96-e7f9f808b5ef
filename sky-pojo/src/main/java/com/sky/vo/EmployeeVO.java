package com.sky.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "员工信息")
public class EmployeeVO {
    @ApiModelProperty("员工id")
    private Integer id;
    @ApiModelProperty("员工账号")
    private String username;
    @ApiModelProperty("员工名称")
    private String name;
    @ApiModelProperty("员工性别")
    private String sex;
    @ApiModelProperty("员工手机号")
    private String phone;
    @ApiModelProperty("账号状态")
    private String status;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("创建人")
    private Long createUser;
    @ApiModelProperty("最后操作人")
    private Long updateUser;
}
