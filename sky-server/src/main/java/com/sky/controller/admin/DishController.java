package com.sky.controller.admin;

import com.sky.dto.DishDTO;
import com.sky.dto.DishPageQueryDTO;
import com.sky.entity.Dish;
import com.sky.result.PageResult;
import com.sky.result.Result;
import com.sky.service.DishService;
import com.sky.vo.DishVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/dish")
@Api("菜品管理")
class DishController {

    @Autowired
    private DishService dishService;

    @PostMapping
    public Result addDesh(@RequestBody DishDTO dish) {
        log.info("添加菜品：{}", dish);
        dishService.addDish(dish);
        return Result.success();
    }

    @GetMapping("/page")
    public Result<PageResult> page(DishPageQueryDTO dishPageQueryDTO) {
        log.info("查询菜品分页列表:{}", dishPageQueryDTO);
        PageResult pageResult = dishService.page(dishPageQueryDTO);
        return Result.success(pageResult);
    }

    @PostMapping("/status/{status}")
    public Result upadteStatus(@PathVariable Integer status, Long id) {
        log.info("修改菜品起售/停售状态：{}", status);
        dishService.updateStatus(status, id);
        return Result.success();
    }

    @GetMapping("/{id}")
    public Result<DishVO> getById(@PathVariable Long id) {
        log.info("根据菜品id查询:{}", id);
        DishVO dish = dishService.getById(id);
        return Result.success(dish);
    }
    @DeleteMapping
    public Result deleteByIds(@RequestParam List<Long> ids) {
        log.info("根据id删除菜品:{}", ids);
        dishService.deleteByIds(ids);
        return Result.success();
    }

    @GetMapping("/list")
    public Result<List<Dish>> list(Long categoryId) {
        log.info("根据分类id查询菜品：{}", categoryId);
       List<Dish> dishs = dishService.getByCategoryId(categoryId);
       return Result.success(dishs);
    }

    @PutMapping
    public Result update(@RequestBody DishDTO dishDTO) {
        log.info("根据id更新菜品：{}", dishDTO);
        dishService.update(dishDTO);
        return Result.success();
    }
}
