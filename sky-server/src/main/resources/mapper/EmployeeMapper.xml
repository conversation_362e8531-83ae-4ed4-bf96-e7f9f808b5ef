<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.EmployeeMapper">
    <select id="findByName" resultType="com.sky.entity.Employee">
        select id, name, username, phone, sex, status, create_time, update_time from employee
        <where>
            <if test="name != null">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
        order by create_time desc
    </select>
    <update id="update">
        update employee
        <set>
            <if test="username != null">
                username=#{username},
            </if>
            <if test="name != null">
                name=#{name},
            </if>
            <if test="password != null">
                password=#{password},
            </if>
            <if test="phone != null">
                phone=#{phone},
            </if>
            <if test="sex != null">
                sex=#{sex},
            </if>
            <if test="idNumber != null">
                id_number=#{idNumber},
            </if>
            <if test="status != null">
                status=#{status},
            </if>
            update_time=#{updateTime},
            update_user=#{updateUser}
        </set>
        where id=#{id}
    </update>
</mapper>
