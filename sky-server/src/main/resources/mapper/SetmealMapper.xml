<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sky.mapper.SetmealMapper">

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into setmeal (category_id, name, price, status, description, image, create_time, update_time, create_user, update_user)
        values (#{categoryId},#{name},#{price},#{status},#{description},#{image},#{createTime},#{updateTime},#{createUser},#{updateUser})
    </insert>
    <select id="page" resultType="com.sky.vo.SetmealVO">
        select s.*, c.name as categoryName from setmeal as s left join category as c  on s.category_id = c.id
        <where>
            <if test="name != null">
                s.name like concat('%', #{name}, '%')
            </if>
            <if test="categoryId != null">
                and s.category_id = #{categoryId}
            </if>
            <if test="status != null">
                and s.status = #{status}
            </if>
        </where>
        order by update_time desc
    </select>
</mapper>